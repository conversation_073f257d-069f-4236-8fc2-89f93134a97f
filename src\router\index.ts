import Login from "@/views/Login.vue";
import MainPage from "@/views/MainPage.vue";
import { createRouter, createWebHashHistory } from "vue-router";

const checkout = () => {
  return localStorage.getItem("User") ? true : false;
};

const router = createRouter({
  history: createWebHashHistory(),
  routes: [
    {
      path: "/",
      name: "/",
      component: () => import("@/views/MainPage.vue"),
    },
  ],
});

//  {
//     path: "/NPT",
//     name: "/NPT",
//     component: () => import("@/components/Development/NPT.vue"),
//   },

export default router;
