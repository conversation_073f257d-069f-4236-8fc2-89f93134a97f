export default {
  lagname: "vi",
  dispatch: {
    pageTitle: "BIỂU PHÂN CÔNG ÉP SẢN XUẤT ÉP ĐẾ XƯỞNG R3",
    model: "<PERSON>ạng",
    color: "Màu",
    size: "SIZE",
    machineID: "<PERSON><PERSON>",
    orderQuantity: "Số Lượng Sản Xuất Đơn Hàng",
    standardRounds: "Số Tua Tiêu <PERSON>",
    type: "Loại",
    ngAnalysis: "Phân Tích Hàng Không Đạt",
    goodProductCount: "Sản Phẩm Tốt",
    total: "Tổng Cộng",
    ng301: "Lỗi Miệng Khuôn",
    ng302: "<PERSON><PERSON><PERSON>",
    ng303: "Lem <PERSON>",
    ng304: "<PERSON><PERSON><PERSON>ết Li<PERSON>",
    ng305: "Bong Bóng",
    ng306: "Bi Lõm",
    ng307: "Hoa Văn Không Rõ",
    ng308: "<PERSON><PERSON><PERSON><PERSON>ẩ<PERSON>",
    ng309: "<PERSON><PERSON><PERSON>",
    ng310: "<PERSON><PERSON><PERSON><PERSON>",
    ng311: "Chồng <PERSON>ấu",
    ng312: "Biến <PERSON>",
    ng313: "<PERSON><PERSON> Dày <PERSON>hông <PERSON>úng",
    ng314: "H<PERSON>ng Không Đạt Đế <PERSON> Su Đúc Tích Hợp",
    ng362: "Nguyên Nhân Khác",
    saveConfirm:
      "Vui lòng kiểm tra thông tin có chính xác không trước khi lưu thay đổi của bạn",
    unSavedPage: "Hoạt động trên phiếu điều phối này chưa được lưu",
    updateSuccess: "Cập nhật phiếu điều phối này thành công",
    updateFail: "Không thể cập nhật phiếu điều phối này",
    noUpdate: "Đã ở trạng thái mới nhất",
    updateWarning:
      "Thông tin không đầy đủ cho người thao tác,vui lòng nhập đầy đủ",
    employeeID: "ST",
    username: "Người thao tác",
    class: "Tổ",
    qcEmployeeID: "KCS'ST",
    qcUsername: "KCS",
    qcClass: "Phụ trách lớp",
    page: "PAGE",
    noData: "Ngày này không có dữ liệu",
    noSelectClass: "Vui lòng chọn lớp ",
    modifyDispath: "Chỉnh sửa báo biểu",
    seeDispath:"Xem báo biểu",
  },
};
