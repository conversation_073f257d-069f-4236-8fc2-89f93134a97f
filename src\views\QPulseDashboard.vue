<template>
  <div class="q-pulse-dashboard">
    <!-- Home Page -->
    <div v-if="currentPage === 'home'" class="home-page">
      <!-- Header -->
      <el-row class="header-section" justify="space-between" align="middle">
        <el-col :span="18">
          <div class="header-left">
            <div class="logo-container">
              <svg class="logo-svg" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                <path class="logo-shape-background" d="M10 40 L50 10 L90 40 L90 90 L10 90 Z" />
              </svg>
              <span class="logo-text-overlay">LY</span>
            </div>
            <h1 class="dashboard-title">Q-Pulse Dashboard</h1>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="current-time">{{ currentTime }}</div>
        </el-col>
      </el-row>

      <!-- KPI Cards -->
      <el-row :gutter="24" class="kpi-section">
        <el-col :span="8">
          <el-card class="kpi-card">
            <div class="kpi-content">
              <el-icon class="kpi-icon success"><TrendCharts /></el-icon>
              <div class="kpi-info">
                <div class="kpi-label">今日全廠通過率</div>
                <div class="kpi-value">93.8%</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="kpi-card">
            <div class="kpi-content">
              <el-icon class="kpi-icon warning"><DocumentChecked /></el-icon>
              <div class="kpi-info">
                <div class="kpi-label">待處理 CAPA</div>
                <div class="kpi-value">8 <span class="overdue">(逾期 3)</span></div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="kpi-card">
            <div class="kpi-content">
              <!-- <el-icon class="kpi-icon danger"><Experiment /></el-icon> -->
              <div class="kpi-info">
                <div class="kpi-label">測試異常棟數</div>
                <div class="kpi-value">3</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- Ticker -->
      <el-card class="ticker-card">
        <div class="ticker-container">
          <div class="ticker-content" :style="{ transform: `translateX(${tickerOffset}px)` }">
            <div v-for="(insight, index) in tickerInsights" :key="index" class="ticker-item" @click="showDetailPage(insight.plantId)">
              <el-icon :class="insight.color"><component :is="insight.icon" /></el-icon>
              <span class="plant-name">{{ insight.plantId }} 棟:</span>
              <span class="insight-text">{{ insight.text }}</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- Plant Cards -->
      <div class="plants-section">
        <h2 class="section-title">棟別風險智慧排序</h2>
        <el-row :gutter="24">
          <el-col v-for="plant in sortedPlants" :key="plant.id" :span="6">
            <el-card class="plant-card" :class="getRiskClass(plant)" @click="showDetailPage(plant.id)">
              <div class="plant-header">
                <h3 class="plant-name">{{ plant.id }}</h3>
                <el-tag :type="getRiskTagType(plant)" size="small">{{ getRiskLevel(plant.riskScore).label }}</el-tag>
              </div>
              <div class="plant-styles">{{ plant.lines.map((l) => l.style).join(", ") }}</div>

              <div class="plant-pass-rate">
                <div class="pass-rate-value">{{ getCurrentPassRate(plant).toFixed(0) }}<span class="percent">%</span></div>
                <div class="pass-rate-label">最近小時通過率</div>
                <div class="smart-hint">
                  <transition name="fade" mode="out-in">
                    <div :key="plant.currentHintIndex" class="hint-content">
                      <el-icon :class="getCurrentHint(plant).color"><component :is="getCurrentHint(plant).icon" /></el-icon>
                      {{ getCurrentHint(plant).text }}
                    </div>
                  </transition>
                </div>
              </div>

              <div class="plant-footer">
                <div class="test-status">
                  <span :class="getBondingFailCount(plant) > 0 ? 'text-warning' : 'text-secondary'">B</span>
                  <span class="separator">/</span>
                  <span :class="getFlexingFailCount(plant) > 0 ? 'text-warning' : 'text-secondary'">F</span>
                </div>
                <div class="capa-status">
                  <el-icon class="text-secondary"><DocumentChecked /></el-icon>
                  <span :class="plant.capaOverdue > 0 ? 'text-danger glow' : 'text-primary'">{{ plant.capaPending }}</span>
                </div>
                <div class="andon-status">
                  <el-icon :class="plant.andon ? 'text-danger glow' : 'text-secondary'"><Bell /></el-icon>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- Detail Page -->
    <div v-if="currentPage === 'detail'" class="detail-page">
      <!-- Detail Header -->
      <el-row class="detail-header" justify="space-between" align="middle">
        <el-col :span="18">
          <h1 class="detail-title">{{ currentPlant?.id }} 棟別詳細分析</h1>
        </el-col>
        <el-col :span="6">
          <el-button type="default" @click="showHomePage">
            <el-icon><ArrowLeft /></el-icon>
            返回總覽
          </el-button>
        </el-col>
      </el-row>

      <!-- Building KPIs -->
      <el-row :gutter="16" class="building-kpis">
        <el-col :span="6">
          <el-card class="kpi-mini-card">
            <div class="kpi-mini-label">平均通過率</div>
            <div class="kpi-mini-value">{{ getBuildingAvgPassRate(currentPlant).toFixed(1) }}%</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="kpi-mini-card">
            <div class="kpi-mini-label">異常產線</div>
            <div class="kpi-mini-value" :class="currentPlant?.andon ? 'text-danger' : ''">
              {{ currentPlant?.andon ? currentPlant.lines.length : 0 }}
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="kpi-mini-card">
            <div class="kpi-mini-label">測試異常</div>
            <div class="kpi-mini-value" :class="currentPlant?.testFails.length > 0 ? 'text-warning' : ''">
              {{ currentPlant?.testFails.length || 0 }}
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="kpi-mini-card">
            <div class="kpi-mini-label">待處理CAPA</div>
            <div class="kpi-mini-value" :class="currentPlant?.capaOverdue > 0 ? 'text-warning' : ''">
              {{ currentPlant?.capaPending || 0 }}
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- Line Selection and Comparison -->
      <div class="line-selection-section">
        <el-row justify="space-between" align="middle">
          <el-col :span="18">
            <h3 class="section-subtitle">產線表現細節</h3>
          </el-col>
          <el-col :span="6">
            <el-button :type="comparisonMode ? 'danger' : 'primary'" @click="toggleComparisonMode">
              <el-icon><component :is="comparisonMode ? 'Close' : 'TrendCharts'" /></el-icon>
              {{ comparisonMode ? "結束比較" : "進入比較模式" }}
            </el-button>
          </el-col>
        </el-row>

        <el-row :gutter="16" class="line-cards">
          <el-col v-for="line in currentPlant?.lines" :key="line.id" :span="6">
            <el-card class="line-card" :class="getLineCardClass(line.id)" @click="handleLineSelection(line.id)">
              <div class="line-header">
                <span class="line-name">{{ line.id }}</span>
                <span class="line-style">{{ line.style }}</span>
              </div>
              <div class="line-pass-rate">{{ line.passRate }}%</div>
              <div class="line-hint">{{ getLineHint(line.id) }}</div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- Charts and Analysis -->
      <el-row :gutter="24" class="charts-section">
        <el-col :span="16">
          <!-- Trend Chart -->
          <el-card class="chart-card">
            <template #header>
              <el-row justify="space-between" align="middle">
                <el-col :span="12">
                  <h3>通過率趨勢圖</h3>
                </el-col>
                <el-col :span="12">
                  <div class="timeframe-controls">
                    <el-button-group>
                      <el-button
                        v-for="period in timeframePeriods"
                        :key="period.value"
                        :type="currentTimeframe === period.value ? 'primary' : 'default'"
                        @click="setTimeframe(period.value)"
                      >
                        {{ period.label }}
                      </el-button>
                    </el-button-group>
                  </div>
                </el-col>
              </el-row>
            </template>
            <div class="chart-container" ref="trendChartRef">
              <!-- Chart will be rendered here -->
            </div>
          </el-card>

          <!-- Pareto Chart -->
          <el-card class="chart-card">
            <template #header>
              <h3>不良原因分析 (Pareto)</h3>
            </template>
            <div class="chart-container" ref="paretoChartRef">
              <!-- Pareto chart will be rendered here -->
            </div>
          </el-card>
        </el-col>

        <el-col :span="8">
          <!-- Test Results -->
          <el-card class="info-card">
            <template #header>
              <h3>BONDING / FLEXING 測試</h3>
            </template>
            <div class="test-results">
              <div v-for="test in testResults" :key="test.type" class="test-item">
                <div class="test-info">
                  <div class="test-name">{{ test.type }}</div>
                  <div class="test-date">日期: {{ test.date }}</div>
                </div>
                <div class="test-stats">
                  <div class="sample-count">
                    抽樣: <strong>{{ test.sampleCount }}</strong>
                  </div>
                  <div class="failure-count" :class="test.failureCount > 0 ? 'text-danger' : 'text-success'">
                    失敗: <strong>{{ test.failureCount }}</strong>
                  </div>
                </div>
              </div>
            </div>
          </el-card>

          <!-- CAPA List -->
          <el-card class="info-card">
            <template #header>
              <h3>CAPA 追蹤</h3>
            </template>
            <div class="capa-list">
              <div v-for="capa in currentCapaList" :key="capa.id" class="capa-item">
                <div class="capa-issue">{{ capa.issue }}</div>
                <div class="capa-details">
                  {{ capa.owner }} | {{ capa.date }} |
                  <span :class="capa.status === '逾期' ? 'text-danger glow' : 'text-warning'">{{ capa.status }}</span>
                </div>
              </div>
              <div v-if="currentCapaList.length === 0" class="no-capa">此棟無待處理CAPA</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from "vue";
import { TrendCharts, DocumentChecked, Bell, ArrowLeft, Close, Warning, CircleCheck } from "@element-plus/icons-vue";

// Reactive data
const currentPage = ref("home");
const currentTime = ref("");
const currentBuildingId = ref(null);
const selectedLineId = ref(null);
const comparisonMode = ref(false);
const comparisonLines = ref([]);
const currentTimeframe = ref("hourly");
const tickerOffset = ref(0);

// Timer references
let timeUpdateInterval = null;
let tickerInterval = null;
let hintRotationIntervals = {};

// Constants
const SMART_HINT_DURATION = 4000;
const timeframePeriods = [
  { label: "時", value: "hourly" },
  { label: "日", value: "daily" },
  { label: "週", value: "weekly" },
  { label: "月", value: "monthly" },
];

// Mock Data
const MOCK_DATA = {
  A2: {
    id: "A2",
    lines: [
      { id: "A2-L1", passRate: 97, style: "Chuck 70" },
      { id: "A2-L2", passRate: 96, style: "Chuck 70" },
    ],
    testFails: [],
    capaPending: 1,
    capaOverdue: 0,
    andon: false,
    passRateYesterday: 96.5,
    peerAvgPassRate: 95.8,
    hourlyPassRates: [95.5, 96.5, 94.5, 97.0, 96.0, 95.5, 94.5, 96.0, 97.0, 96.0, 96.5, 95.8, 96.2, 97.0, 96.4, 96.5, 97.0, 97.8, 97.0, 96.5, 96.0, 95.5, 95.0, 94.5],
    dailyPassRates: [95.5, 96.0, 95.2, 96.0, 96.8, 97.1, 96.0],
    lineDataDetails: {
      "A2-L1": {
        hourlyPassRates: [95, 96, 94, 98, 97, 95, 96, 94, 98, 97, 97.5, 96.8, 97.2, 98.1, 96.9, 97.0, 97.5, 97.8, 97.0, 96.5, 96.0, 95.5, 95.0, 94.5],
        defectData: [
          { name: "脫膠", value: 20 },
          { name: "髒汙", value: 15 },
          { name: "線跡偏差", value: 10 },
          { name: "氣泡", value: 5 },
          { name: "油漬", value: 4 },
        ],
      },
      "A2-L2": {
        hourlyPassRates: [96, 97, 95, 96, 95, 94, 93, 92, 91, 90, 89.5, 88.8, 89.2, 90.1, 91.9, 92.0, 92.5, 93.8, 94.0, 95.5, 96.0, 96.5, 97.0, 97.5],
        defectData: [
          { name: "刮傷", value: 18 },
          { name: "氣泡", value: 12 },
          { name: "溢膠", value: 8 },
          { name: "油漬", value: 4 },
          { name: "色差", value: 3 },
        ],
      },
    },
  },
  A3: {
    id: "A3",
    lines: [
      { id: "A3-L1", passRate: 88, style: "Fastbreak Pro" },
      { id: "A3-L2", passRate: 90, style: "One Star" },
    ],
    testFails: [{ date: "07/18", type: "B" }],
    capaPending: 2,
    capaOverdue: 1,
    andon: true,
    passRateYesterday: 92.1,
    peerAvgPassRate: 93.5,
    hourlyPassRates: [92, 91, 88, 87, 90, 89, 88, 87, 86, 85, 84, 83, 82, 81, 80, 79, 78, 77, 76, 75, 74, 73, 72, 71],
    dailyPassRates: [92.1, 91.5, 88.0, 87.5, 89.0, 88.5, 87.0],
    lineDataDetails: {
      "A3-L1": {
        hourlyPassRates: [92, 91, 88, 87, 90, 89, 88, 87, 86, 85, 84, 83, 82, 81, 80, 79, 78, 77, 76, 75, 74, 73, 72, 71],
        defectData: [
          { name: "脫膠", value: 40 },
          { name: "髒汙", value: 30 },
          { name: "線跡偏差", value: 20 },
          { name: "針距不均", value: 10 },
          { name: "刮傷", value: 8 },
        ],
      },
      "A3-L2": {
        hourlyPassRates: [90, 89, 91, 88, 87, 86, 85, 84, 83, 82, 81, 80, 79, 78, 77, 76, 75, 74, 73, 72, 71, 70, 69, 68],
        defectData: [
          { name: "溢膠", value: 35 },
          { name: "材料變形", value: 25 },
          { name: "異物混入", value: 15 },
          { name: "尺寸偏差", value: 8 },
          { name: "刮傷", value: 7 },
        ],
      },
    },
  },
  A7: {
    id: "A7",
    lines: [{ id: "A7-L1", passRate: 99, style: "One Star" }],
    testFails: [],
    capaPending: 0,
    capaOverdue: 0,
    andon: false,
    passRateYesterday: 98.2,
    peerAvgPassRate: 95.8,
    hourlyPassRates: [98, 99, 97, 98, 99, 98, 99, 97, 98, 99, 99.5, 99.0, 98.8, 99.1, 99.3, 99.0, 98.9, 99.2, 99.5, 99.0, 98.8, 99.1, 99.3, 99.0],
    dailyPassRates: [98.2, 98.5, 98.0, 99.0, 98.8, 99.1, 99.5],
    lineDataDetails: {
      "A7-L1": {
        hourlyPassRates: [98, 99, 97, 98, 99, 98, 99, 97, 98, 99, 99.5, 99.0, 98.8, 99.1, 99.3, 99.0, 98.9, 99.2, 99.5, 99.0, 98.8, 99.1, 99.3, 99.0],
        defectData: [
          { name: "外觀微瑕", value: 10 },
          { name: "輕微色差", value: 5 },
          { name: "線頭外露", value: 3 },
          { name: "無不良", value: 0.1 },
          { name: "輕微氣泡", value: 0.5 },
        ],
      },
    },
  },
  A8: {
    id: "A8",
    lines: [{ id: "A8-L1", passRate: 92, style: "Chuck 70" }],
    testFails: [],
    capaPending: 1,
    capaOverdue: 0,
    andon: false,
    passRateYesterday: 92.5,
    peerAvgPassRate: 95.8,
    hourlyPassRates: [93, 94, 90, 91, 92, 93, 94, 90, 91, 92, 92.5, 93.0, 92.0, 91.5, 92.8, 92.0, 91.5, 92.8, 92.0, 91.5, 92.8, 92.0, 91.5, 92.8],
    dailyPassRates: [92.5, 92.0, 93.0, 91.5, 92.8, 92.0, 91.5],
    lineDataDetails: {
      "A8-L1": {
        hourlyPassRates: [93, 94, 90, 91, 92, 93, 94, 90, 91, 92, 92.5, 93.0, 92.0, 91.5, 92.8, 92.0, 91.5, 92.8, 92.0, 91.5, 92.8, 92.0, 91.5, 92.8],
        defectData: [
          { name: "線頭外露", value: 22 },
          { name: "油漬", value: 16 },
          { name: "針距不均", value: 11 },
          { name: "膠水痕跡", value: 7 },
          { name: "輕微刮傷", value: 5 },
        ],
      },
    },
  },
};

// Ticker insights data
const tickerInsights = ref([
  { plantId: "A16", text: "已連續3小時通過率低於90%", icon: "TrendCharts", color: "text-danger" },
  { plantId: "A3", text: "通過率低於昨日-2.1%", icon: "TrendCharts", color: "text-warning" },
  { plantId: "A9", text: "最新測試報告: Bonding 測試失敗", icon: "Experiment", color: "text-warning" },
  { plantId: "A3", text: "CAPA 已逾期", icon: "DocumentChecked", color: "text-warning" },
]);

// Test results data
const testResults = ref([
  { type: "Bonding Test", sampleCount: 4, failureCount: 0, date: "07/19" },
  { type: "Flexing Test", sampleCount: 5, failureCount: 0, date: "07/19" },
  { type: "Material Comp. Test", sampleCount: 3, failureCount: 0, date: "07/18" },
]);

// CAPA data
const capaData = ref([
  { id: 1, plant: "A3", issue: "成型不良導致脫膠", status: "逾期", owner: "線長A", date: "07/15" },
  { id: 2, plant: "A16", issue: "鞋面大面積髒汙", status: "處理中", owner: "線長B", date: "07/18" },
  { id: 3, plant: "A15", issue: "裁斷尺寸偏差", status: "處理中", owner: "線長C", date: "07/19" },
  { id: 4, plant: "A3", issue: "高溫試驗變形", status: "逾期", owner: "線長D", date: "07/10" },
]);

// Computed properties
const sortedPlants = computed(() => {
  return Object.values(MOCK_DATA)
    .map((plant) => ({ ...plant, riskScore: calculateRiskScore(plant) }))
    .sort((a, b) => b.riskScore - a.riskScore);
});

const currentPlant = computed(() => {
  return currentBuildingId.value ? MOCK_DATA[currentBuildingId.value] : null;
});

const currentCapaList = computed(() => {
  return capaData.value.filter((c) => c.plant === currentBuildingId.value);
});

// Methods
const calculateRiskScore = (plant) => {
  let score = 0;
  const rates = plant.hourlyPassRates;
  let consecutiveLow = 0;

  for (let i = rates.length - 1; i >= 0; i--) {
    if (rates[i] < 92) consecutiveLow++;
    else break;
  }

  if (consecutiveLow >= 3) score += 40;
  else if (consecutiveLow >= 2) score += 20;

  const latestRate = rates[rates.length - 1];
  if (latestRate < 90) score += 15;
  else if (latestRate < 93) score += 5;

  if (plant.testFails && plant.testFails.length > 0) score += 25 * plant.testFails.length;
  if (plant.capaOverdue > 0) score += 20;
  if (plant.andon) score += 15;
  score += plant.capaPending * 3;

  return score;
};

const getRiskLevel = (score) => {
  if (score >= 60) return { class: "risk-high", textClass: "text-danger", label: "高風險" };
  if (score >= 40) return { class: "risk-medium", textClass: "text-warning", label: "中風險" };
  if (score >= 15) return { class: "risk-low", textClass: "text-warning", label: "低風險" };
  return { class: "risk-normal", textClass: "text-success", label: "正常" };
};

const getRiskClass = (plant) => {
  const risk = getRiskLevel(plant.riskScore);
  return risk.class;
};

const getRiskTagType = (plant) => {
  const risk = getRiskLevel(plant.riskScore);
  if (risk.class === "risk-high") return "danger";
  if (risk.class === "risk-medium") return "warning";
  if (risk.class === "risk-low") return "warning";
  return "success";
};

const getCurrentPassRate = (plant) => {
  return plant.hourlyPassRates[plant.hourlyPassRates.length - 1];
};

const getBondingFailCount = (plant) => {
  return plant.testFails.filter((f) => f.type === "B").length;
};

const getFlexingFailCount = (plant) => {
  return plant.testFails.filter((f) => f.type === "F").length;
};

const getBuildingAvgPassRate = (plant) => {
  if (!plant || !plant.hourlyPassRates) return 0;
  return plant.hourlyPassRates.reduce((sum, rate) => sum + rate, 0) / plant.hourlyPassRates.length;
};

const getCurrentHint = (plant) => {
  const hints = getSmartHintsForPlant(plant.id);
  if (!hints.length) return { text: "表現穩定良好", color: "text-success", icon: "CircleCheck" };
  return hints[plant.currentHintIndex || 0];
};

const getSmartHintsForPlant = (plantId) => {
  const plant = MOCK_DATA[plantId];
  if (!plant) return [];

  const hints = [];
  const currentRate = plant.hourlyPassRates[plant.hourlyPassRates.length - 1];

  // Compare with yesterday
  const vsYesterday = currentRate - plant.passRateYesterday;
  if (vsYesterday < -1.0) {
    hints.push({
      text: `較昨日退步 ${Math.abs(vsYesterday).toFixed(1)}%`,
      color: "text-danger",
      icon: "TrendCharts",
    });
  } else if (vsYesterday > 1.0) {
    hints.push({
      text: `較昨日進步 ${vsYesterday.toFixed(1)}%`,
      color: "text-success",
      icon: "TrendCharts",
    });
  }

  // Check consecutive low hours
  let consecutiveLowHours = 0;
  for (let i = plant.hourlyPassRates.length - 1; i >= 0; i--) {
    if (plant.hourlyPassRates[i] < 90) {
      consecutiveLowHours++;
    } else {
      break;
    }
  }

  if (consecutiveLowHours >= 3) {
    hints.push({
      text: `已連續 ${consecutiveLowHours} 小時通過率低於90%`,
      color: "text-danger",
      icon: "Warning",
    });
  }

  // CAPA overdue
  if (plant.capaOverdue > 0) {
    hints.push({
      text: `有 ${plant.capaOverdue} 項 CAPA 已逾期`,
      color: "text-warning",
      icon: "DocumentChecked",
    });
  }

  if (hints.length === 0) {
    hints.push({
      text: "表現穩定良好",
      color: "text-success",
      icon: "CircleCheck",
    });
  }

  return hints;
};

const getLineCardClass = (lineId) => {
  let classes = [];
  if (selectedLineId.value === lineId) classes.push("selected");
  if (comparisonMode.value) {
    classes.push("comparison-active");
    if (comparisonLines.value.includes(lineId)) classes.push("comparison-selected");
  }
  return classes.join(" ");
};

const getLineHint = (lineId) => {
  const plant = currentPlant.value;
  if (!plant || !plant.lineDataDetails[lineId]) return "(無數據)";

  const lineDetails = plant.lineDataDetails[lineId];
  const currentRate = lineDetails.hourlyPassRates[lineDetails.hourlyPassRates.length - 1];
  const buildingAvg = getBuildingAvgPassRate(plant);
  const diff = currentRate - buildingAvg;

  if (Math.abs(diff) > 1) {
    const isBetter = diff > 0;
    return `(較本棟平均 ${isBetter ? "高" : "低"} ${Math.abs(diff).toFixed(1)}%)`;
  }
  return "(表現穩定)";
};

// Navigation methods
const showDetailPage = (buildingId) => {
  currentBuildingId.value = buildingId;
  selectedLineId.value = null;
  comparisonMode.value = false;
  comparisonLines.value = [];
  currentPage.value = "detail";

  // Update test results for current plant
  const plant = MOCK_DATA[buildingId];
  testResults.value = [
    { type: "Bonding Test", sampleCount: 4, failureCount: getBondingFailCount(plant), date: "07/19" },
    { type: "Flexing Test", sampleCount: 5, failureCount: getFlexingFailCount(plant), date: "07/19" },
    { type: "Material Comp. Test", sampleCount: 3, failureCount: 0, date: "07/18" },
  ];
};

const showHomePage = () => {
  currentPage.value = "home";
  currentBuildingId.value = null;
};

const handleLineSelection = (lineId) => {
  if (comparisonMode.value) {
    const index = comparisonLines.value.indexOf(lineId);
    if (index > -1) {
      comparisonLines.value.splice(index, 1);
    } else {
      comparisonLines.value.push(lineId);
    }
  } else {
    selectedLineId.value = selectedLineId.value === lineId ? null : lineId;
  }
};

const toggleComparisonMode = () => {
  comparisonMode.value = !comparisonMode.value;
  if (comparisonMode.value) {
    if (selectedLineId.value && !comparisonLines.value.includes(selectedLineId.value)) {
      comparisonLines.value.push(selectedLineId.value);
    }
    selectedLineId.value = null;
  } else {
    comparisonLines.value = [];
  }
};

const setTimeframe = (period) => {
  currentTimeframe.value = period;
};

// Time update
const updateCurrentTime = () => {
  const now = new Date();
  currentTime.value = now.toLocaleString("zh-TW", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
  });
};

// Ticker animation
const startTickerAnimation = () => {
  tickerInterval = setInterval(() => {
    tickerOffset.value -= 1;
    if (tickerOffset.value <= -1000) {
      tickerOffset.value = 0;
    }
  }, 50);
};

// Lifecycle hooks
onMounted(() => {
  updateCurrentTime();
  timeUpdateInterval = setInterval(updateCurrentTime, 1000);
  startTickerAnimation();

  // Initialize hint rotation for plants
  Object.keys(MOCK_DATA).forEach((plantId) => {
    MOCK_DATA[plantId].currentHintIndex = 0;
    hintRotationIntervals[plantId] = setInterval(() => {
      const hints = getSmartHintsForPlant(plantId);
      if (hints.length > 1) {
        MOCK_DATA[plantId].currentHintIndex = (MOCK_DATA[plantId].currentHintIndex + 1) % hints.length;
      }
    }, SMART_HINT_DURATION);
  });
});

onUnmounted(() => {
  if (timeUpdateInterval) clearInterval(timeUpdateInterval);
  if (tickerInterval) clearInterval(tickerInterval);
  Object.values(hintRotationIntervals).forEach((interval) => clearInterval(interval));
});
</script>

<style scoped>
.q-pulse-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #e0e5ec 0%, #f0f2f5 100%);
  padding: 20px;
  font-family: "Inter", "Noto Sans TC", sans-serif;
}

/* Header Styles */
.header-section {
  margin-bottom: 24px;
}

.header-left {
  display: flex;
  align-items: center;
}

.logo-container {
  height: 56px;
  width: 56px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  margin-right: 16px;
}

.logo-svg {
  width: 100%;
  height: 100%;
}

.logo-shape-background {
  fill: #facc15;
  filter: drop-shadow(0px 5px 8px rgba(0, 0, 0, 0.1));
}

.logo-text-overlay {
  position: absolute;
  font-family: "Inter", sans-serif;
  font-weight: 800;
  font-size: 32px;
  color: #22c55e;
  text-shadow: 0px 3px 5px rgba(0, 0, 0, 0.1);
}

.dashboard-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
}

.current-time {
  text-align: right;
  color: #4a5568;
  font-weight: 300;
}

/* KPI Cards */
.kpi-section {
  margin-bottom: 32px;
}

.kpi-card {
  background: rgba(255, 255, 255, 0.88);
  backdrop-filter: blur(38px) saturate(180%);
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 16px;
  box-shadow:
    inset 0px 1px 5px rgba(0, 0, 0, 0.08),
    0px 20px 60px rgba(0, 0, 0, 0.22);
}

.kpi-content {
  display: flex;
  align-items: center;
  padding: 8px;
}

.kpi-icon {
  font-size: 2rem;
  width: 48px;
  text-align: center;
  margin-right: 16px;
}

.kpi-icon.success {
  color: #22c55e;
}

.kpi-icon.warning {
  color: #eab308;
}

.kpi-icon.danger {
  color: #ef4444;
}

.kpi-info {
  flex: 1;
}

.kpi-label {
  font-size: 0.875rem;
  color: #4a5568;
  font-weight: 300;
  margin-bottom: 4px;
}

.kpi-value {
  font-size: 1.875rem;
  font-weight: 700;
  color: #1a202c;
}

.overdue {
  font-size: 1rem;
  font-weight: 400;
  color: #ef4444;
}

/* Ticker */
.ticker-card {
  margin-bottom: 32px;
  border-radius: 9999px;
  background: rgba(255, 255, 255, 0.88);
  backdrop-filter: blur(38px) saturate(180%);
  border: 1px solid rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.ticker-container {
  width: 100%;
  overflow: hidden;
  height: 60px;
  display: flex;
  align-items: center;
}

.ticker-content {
  display: flex;
  white-space: nowrap;
  transition: transform 0.1s linear;
}

.ticker-item {
  display: inline-flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.08);
  padding: 12px 24px;
  border-radius: 9999px;
  margin: 0 12px;
  border: 1px solid rgba(0, 0, 0, 0.12);
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
  color: #1a202c;
}

.ticker-item:hover {
  background-color: rgba(0, 0, 0, 0.15);
}

.plant-name {
  font-weight: 600;
  margin-left: 8px;
}

.insight-text {
  margin-left: 8px;
  color: #4a5568;
  font-weight: 300;
}

/* Plants Section */
.plants-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 16px;
}

.plant-card {
  background: rgba(255, 255, 255, 0.88);
  backdrop-filter: blur(38px) saturate(180%);
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 16px;
  box-shadow:
    inset 0px 1px 5px rgba(0, 0, 0, 0.08),
    0px 20px 60px rgba(0, 0, 0, 0.22);
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.5, 1);
  padding: 24px;
}

.plant-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    inset 0px 1px 5px rgba(0, 0, 0, 0.1),
    0 40px 80px rgba(0, 0, 0, 0.22);
  border: 1px solid rgba(0, 0, 0, 0.2);
}

.plant-card.risk-high {
  border-left: 4px solid #ef4444;
}

.plant-card.risk-medium {
  border-left: 4px solid #f97316;
}

.plant-card.risk-low {
  border-left: 4px solid #eab308;
}

.plant-card.risk-normal {
  border-left: 4px solid #22c55e;
}

.plant-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.plant-name {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
}

.plant-styles {
  font-size: 0.875rem;
  color: #4a5568;
  font-weight: 300;
  margin-bottom: 24px;
}

.plant-pass-rate {
  text-align: center;
  margin: 24px 0;
}

.pass-rate-value {
  font-size: 4rem;
  font-weight: 700;
  color: #1a202c;
  line-height: 1;
}

.percent {
  font-size: 2rem;
  color: #4a5568;
  font-weight: 500;
}

.pass-rate-label {
  font-size: 0.875rem;
  color: #4a5568;
  font-weight: 300;
  margin-top: -4px;
  margin-bottom: 16px;
}

.smart-hint {
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.hint-content {
  font-size: 0.875rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.plant-footer {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.test-status,
.capa-status,
.andon-status {
  display: flex;
  align-items: center;
  font-weight: 700;
  font-size: 1.125rem;
}

.separator {
  margin: 0 6px;
  color: #4a5568;
}

.text-warning {
  color: #f97316;
}

.text-secondary {
  color: #4a5568;
}

.text-danger {
  color: #ef4444;
}

.text-primary {
  color: #1a202c;
}

.text-success {
  color: #22c55e;
}

.glow {
  animation: text-glow 2s ease-in-out infinite;
}

@keyframes text-glow {
  0%,
  100% {
    text-shadow:
      0 0 8px rgba(239, 68, 68, 0.8),
      0 0 16px rgba(239, 68, 68, 0.6);
  }
  50% {
    text-shadow:
      0 0 16px rgba(239, 68, 68, 0.9),
      0 0 32px rgba(239, 68, 68, 0.7);
  }
}

/* Detail Page Styles */
.detail-page {
  width: 100%;
}

.detail-header {
  margin-bottom: 24px;
}

.detail-title {
  font-size: 1.875rem;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
}

.building-kpis {
  margin-bottom: 24px;
}

.kpi-mini-card {
  text-align: center;
  background: rgba(255, 255, 255, 0.88);
  backdrop-filter: blur(38px) saturate(180%);
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 12px;
  padding: 16px;
}

.kpi-mini-label {
  font-size: 0.875rem;
  color: #4a5568;
  margin-bottom: 8px;
}

.kpi-mini-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1a202c;
}

.line-selection-section {
  margin-bottom: 24px;
}

.section-subtitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 16px;
}

.line-cards {
  margin-top: 16px;
}

.line-card {
  background: rgba(255, 255, 255, 0.88);
  backdrop-filter: blur(38px) saturate(180%);
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.line-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.line-card.selected {
  border-color: #6366f1;
  background-color: rgba(99, 102, 241, 0.12);
}

.line-card.comparison-active {
  background-color: rgba(0, 0, 0, 0.05);
}

.line-card.comparison-selected {
  border-color: #eab308;
  background-color: rgba(234, 179, 8, 0.12);
}

.line-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.line-name {
  font-weight: 600;
  color: #1a202c;
}

.line-style {
  font-size: 0.875rem;
  color: #4a5568;
}

.line-pass-rate {
  text-align: right;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 8px;
}

.line-hint {
  font-size: 0.75rem;
  color: #4a5568;
  text-align: center;
  height: 16px;
}

.charts-section {
  margin-bottom: 24px;
}

.chart-card,
.info-card {
  background: rgba(255, 255, 255, 0.88);
  backdrop-filter: blur(38px) saturate(180%);
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 16px;
  margin-bottom: 24px;
}

.timeframe-controls {
  display: flex;
  justify-content: flex-end;
}

.chart-container {
  width: 100%;
  height: 320px;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.test-results,
.capa-list {
  max-height: 300px;
  overflow-y: auto;
}

.test-item,
.capa-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 8px;
  margin-bottom: 12px;
}

.test-info,
.capa-issue {
  flex: 1;
}

.test-name {
  font-weight: 600;
  color: #1a202c;
}

.test-date,
.capa-details {
  font-size: 0.75rem;
  color: #4a5568;
  margin-top: 4px;
}

.test-stats {
  text-align: right;
}

.sample-count,
.failure-count {
  font-size: 0.875rem;
}

.no-capa {
  text-align: center;
  color: #4a5568;
  padding: 16px;
}

/* Transitions */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .q-pulse-dashboard {
    padding: 12px;
  }

  .dashboard-title {
    font-size: 1.5rem;
  }

  .pass-rate-value {
    font-size: 3rem;
  }

  .plant-card {
    margin-bottom: 16px;
  }

  .logo-container {
    height: 40px;
    width: 40px;
  }

  .logo-text-overlay {
    font-size: 24px;
  }
}
</style>
