// Plugins
import Vue from "@vitejs/plugin-vue";
import path from 'path';
import AutoImport from "unplugin-auto-import/vite";
import ViteFonts from "unplugin-fonts/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";
import Components from "unplugin-vue-components/vite";
import Vuetify, { transformAssetUrls } from "vite-plugin-vuetify";

// Utilities
import { defineConfig } from "vite";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    Vue({
      template: { transformAssetUrls },
    }),
    Vuetify(),
    Components(),
    ViteFonts({
      google: {
        families: [
          {
            name: "Roboto",
            styles: "wght@100;300;400;500;700;900",
          },
        ],
      },
    }),
    AutoImport({
      resolvers: [ElementPlusResolver()],
    }),
    Components({
      resolvers: [ElementPlusResolver()],
    }),
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  define: { "process.env": {} },
  server: {
    host: "0.0.0.0", // 使服务器在局域网内可访问
    port: 8002,
    open: false, // 启动时自动打开浏览器
    hmr: {
      protocol: "ws", // wss => http |ws => http
      host: "localhost", // 主机
      port: 8002,
    },
  },
});
