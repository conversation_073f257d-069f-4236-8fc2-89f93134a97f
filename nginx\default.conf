server {
        listen          80;
        server_name     **************;
        root            /usr/share/nginx/export-excel;
        index           index.html;


    # Proxy for Excel files
location /excel-api/ {
    proxy_pass https://www.tyxuan.com.vn/strapi/uploads/;
    proxy_set_header Host www.tyxuan.com.vn;
    proxy_ssl_server_name on;
    proxy_buffer_size 128k;
    proxy_buffers 4 256k;
    proxy_busy_buffers_size 256k;

    # Force binary content type
    proxy_hide_header Content-Type;
    add_header Content-Type "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" always;
    add_header Content-Disposition "attachment; filename=template.xlsx" always;

    # Binary data handling
    proxy_http_version 1.1;
    proxy_set_header Connection '';
    proxy_buffering off;
}

        location / {
            try_files $uri $uri/ /index.html;

        add_header 'Access-Control-Allow-Origin' '*';
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
        add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range';

    if ($request_method = 'OPTIONS') {
        add_header 'Access-Control-Allow-Origin' '*';
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
        add_header 'Access-Control-Max-Age' 1728000;
        add_header 'Content-Type' 'text/plain; charset=utf-8';
        add_header 'Content-Length' 0;
        return 204;
    }

            # Additional security headers
            add_header X-Frame-Options "SAMEORIGIN" always;
            add_header X-XSS-Protection "1; mode=block" always;
            add_header X-Content-Type-Options "nosniff" always;
            add_header Referrer-Policy "no-referrer-when-downgrade" always;
        }

}
