import { ref } from "vue";
import { ElLoading } from "element-plus";
import { ElNotification } from "element-plus";
const loading = ref(null);

const showLoading = (text = "Loading...") => {
  loading.value = ElLoading.service({
    lock: true,
    text,
    background: "rgba(0, 0, 0, 0.7)",
  });
};

const hideLoading = () => {
  if (loading.value) {
    loading.value.close();
    loading.value = null;
  }
};

const Success = (message) => {
  ElNotification({
    title: "Success",
    message: typeof message === "string" ? message : message.toString(),
    type: "success",
  });
};

const Warning = (message) => {
  ElNotification({
    title: "Warning",
    message: typeof message === "string" ? message : message.toString(),
    type: "warning",
  });
};

const Info = (message) => {
  ElNotification({
    title: "Info",
    message: typeof message === "string" ? message : message.toString(),
    type: "info",
  });
};

const Error = (message) => {
  ElNotification({
    title: "Error",
    message: typeof message === "string" ? message : message.toString(),
    type: "error",
  });
};

export { loading, showLoading, hideLoading, Success, Warning, Info, Error };
