<template>
  <div class="body-login">
    <video autoplay muted loop playsinline class="background-video">
      <source src="/src/assets/Login-Assets/0324.webm" type="video/webm" />
      Your browser does not support the video tag.
    </video>
    <div class="background-overlay"></div>
    <img src="/src/assets/Login-Assets/logoLY.png" alt="Company Logo" class="logo" />
    <img src="/src/assets/Login-Assets/vector1.png" alt="Corner Image" class="corner-image" />
    <img src="/src/assets/Login-Assets/vector2.png" alt="Corner Image" class="corner-image2" />
    <img src="/src/assets/Login-Assets/vector3.png" alt="Corner Image" class="corner-image3" />
    <div class="development-text">Development</div>
    <div class="container-login">
      <div class="login-box">
        <h1>LOGIN</h1>
        <p>Please enter your username and password!</p>
        <input type="text" placeholder="Username" v-model="username" />
          <div class="input-container-login">
            <input :type="passwordVisible ? 'text' : 'password'" id="password" placeholder="Password" v-model="password" />
            <img :src="passwordVisible ? openEye : closeEye" class="toggle-password" @click="togglePassword" alt="Toggle Password" />
          </div>
          <button @click="login"><b>LOGIN</b></button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { Login_ERP } from "@/api/login";
import closeEye from "@/assets/Login-Assets/closeeye.png";
import openEye from "@/assets/Login-Assets/openeye.png";
import { error, success } from "@/utils/NotificationsView";
import "@mdi/font/css/materialdesignicons.css";
import { ref } from "vue";
import { useRouter } from "vue-router";
const router = useRouter();
const username = ref("");
const password = ref("");

const passwordVisible = ref(false);
const togglePassword = () => {
  passwordVisible.value = !passwordVisible.value;
};

const login = async () => {
  try {
    const User = {
      USERID: username.value,
      PWD: password.value,
    };

    const res = await Login_ERP(User);
    localStorage.setItem("User", JSON.stringify(res.data.data));
    success("Login successfully!");
    router.push("/Home");
  } catch (e) {
    console.error(e);
    error(e.response.data.message);
  }
};
</script>

<style lang="css" scoped>
.body-login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100vw;
  margin: 0;
  font-family: Arial, sans-serif;
  position: relative;
  overflow: hidden;
}
.background-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: -2;
}
.background-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: -1;
}
.logo {
  position: absolute;
  top: -3%;
  left: 10px;
  width: 200px;
  height: auto;
}
.corner-image {
  position: absolute;
  bottom: 0%;
  left: 0%;
  width: 100%;
  height: 100%;
  z-index: 1;
}
.corner-image2 {
  position: absolute;
  bottom: 30%;
  left: -2%;
  width: 20%;
  height: auto;
  z-index: 1;
}
.corner-image3 {
  position: absolute;
  bottom: -6%;
  left: 80%;
  width: 20%;
  height: auto;
  z-index: 1;
}
.container-login {
  display: flex;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
  width: 60%;
  max-width: 400px;
  padding: 20px;
  position: relative;
  z-index: 1;
  justify-content: space-between;
  margin-left: 10%;
}
.login-box {
  max-width: 300px;
  margin: 0 auto;
  padding: 1px;
}
.development-text {
  color: white;
  font-size: 100px;
  font-weight: bold;
  margin-right: 1px;
  align-self: right;
  position: relative;
  text-transform: uppercase;
  animation: glitch 0.5s infinite;
}
@keyframes glitch {
  0% {
    text-shadow:
      2px 2px red,
      -2px -2px blue;
  }
  25% {
    text-shadow:
      -2px -2px red,
      2px 2px blue;
  }
  50% {
    text-shadow:
      2px -2px red,
      -2px 2px blue;
  }
  75% {
    text-shadow:
      -2px 2px red,
      2px -2px blue;
  }
  100% {
    text-shadow:
      2px 2px red,
      -2px -2px blue;
  }
}
h1 {
  text-align: center;
  margin-bottom: 1px;
  color: white;
  text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.7);
  opacity: 0;
  margin-left: 12px;
  animation: fadeInDown 1s ease-in-out forwards;
}
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
p {
  text-align: center;
  color: white;
  margin-bottom: 1px;
  font-size: 12px;
  white-space: nowrap;
  margin-left: 12px;
}
.input-container-login {
  position: relative;
  width: 100%;
}
input {
  width: 100%;
  padding: 10px;
  margin: 10px 0;
  border: 1px solid #ccc;
  border-radius: 5px;
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.3);
  opacity: 0;
}
input[type="text"] {
  animation: fadeInUp 1s ease-in-out 0.2s forwards;
}
input[type="password"] {
  animation: fadeInUp 1s ease-in-out 0.4s forwards;
}
.toggle-password {
  position: absolute;
  right: 5px;
  top: 35%;
  transform: translateY(-50%);
  cursor: pointer;
  width: 20px;
  height: 20px;
  opacity: 0;
  animation: fadeInUp 1s ease-in-out 0.4s forwards;
}
button {
  width: 107%;
  padding: 10px;
  background: linear-gradient(90deg, red, orange, yellow, green, blue, indigo, violet);
  background-size: 400% 400%;
  color: white;
  border: none;
  border-radius: 30px;
  cursor: pointer;
  margin-top: 10px;
  animation: rainbow 30s infinite linear;
}
@keyframes rainbow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
</style>
