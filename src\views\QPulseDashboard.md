# Q-Pulse Dashboard - Vue 3 + Element Plus

## 概述

這是一個使用 Vue 3 + Element Plus 重新構建的 Q-Pulse Dashboard，原本是 HTML/JavaScript 實現，現在轉換為現代化的 Vue 組件。

## 功能特點

### 🏠 首頁 (Home Page)
- **實時 KPI 顯示**: 今日全廠通過率、待處理 CAPA、測試異常棟數
- **智慧跑馬燈**: 顯示重要警示和洞察信息
- **棟別風險排序**: 根據風險評分自動排序的棟別卡片
- **智慧提示輪播**: 每個棟別卡片顯示動態智慧提示

### 📊 詳細頁面 (Detail Page)
- **棟別 KPI**: 平均通過率、異常產線、測試異常、待處理 CAPA
- **產線選擇**: 支持單選和比較模式
- **趨勢圖表**: 時/日/週/月 通過率趨勢
- **Pareto 分析**: 不良原因分析圖表
- **測試結果**: BONDING/FLEXING 測試狀態
- **CAPA 追蹤**: 待處理和逾期 CAPA 列表

## 技術架構

### 使用的技術棧
- **Vue 3**: 使用 Composition API
- **Element Plus**: UI 組件庫
- **TypeScript**: 類型安全
- **Pinia**: 狀態管理 (如需要)
- **Vue Router**: 路由管理

### 組件結構
```
QPulseDashboard.vue
├── Template (模板)
│   ├── Home Page (首頁)
│   │   ├── Header (標題欄)
│   │   ├── KPI Cards (KPI 卡片)
│   │   ├── Ticker (跑馬燈)
│   │   └── Plant Cards (棟別卡片)
│   └── Detail Page (詳細頁)
│       ├── Detail Header (詳細頁標題)
│       ├── Building KPIs (棟別 KPI)
│       ├── Line Selection (產線選擇)
│       └── Charts Section (圖表區域)
├── Script (邏輯)
│   ├── Reactive Data (響應式數據)
│   ├── Computed Properties (計算屬性)
│   ├── Methods (方法)
│   └── Lifecycle Hooks (生命週期)
└── Style (樣式)
    ├── Glass Morphism Design (毛玻璃設計)
    ├── Responsive Layout (響應式布局)
    └── Smooth Animations (流暢動畫)
```

## 主要功能

### 風險評分算法
```javascript
const calculateRiskScore = (plant) => {
  let score = 0
  // 連續低通過率
  // 測試失敗次數
  // CAPA 逾期
  // 安燈警示
  return score
}
```

### 智慧提示系統
- 與昨日通過率比較
- 連續低通過率警示
- CAPA 逾期提醒
- 表現穩定提示

### 比較模式
- 支持多條產線比較
- 動態圖表更新
- 視覺化差異顯示

## 樣式設計

### 設計理念
- **毛玻璃效果**: 使用 backdrop-filter 實現現代化視覺效果
- **暗色主題**: 支持 Element Plus 暗色主題
- **響應式設計**: 適配手機、平板、桌面設備
- **微動畫**: 提升用戶體驗的細微動畫效果

### 顏色系統
```css
:root {
  --accent-indigo: #6366f1;
  --accent-red: #ef4444;
  --accent-orange: #f97316;
  --accent-yellow: #eab308;
  --accent-green: #22c55e;
}
```

## 使用方法

### 路由配置
```javascript
{
  path: "/qpulse",
  name: "QPulseDashboard", 
  component: () => import("@/views/QPulseDashboard.vue")
}
```

### 組件引用
```vue
<template>
  <QPulseDashboard />
</template>

<script setup>
import QPulseDashboard from '@/views/QPulseDashboard.vue'
</script>
```

## 數據結構

### 棟別數據格式
```javascript
{
  id: 'A2',
  lines: [
    { id: 'A2-L1', passRate: 97, style: 'Chuck 70' }
  ],
  testFails: [],
  capaPending: 1,
  capaOverdue: 0,
  andon: false,
  hourlyPassRates: [95.5, 96.5, ...],
  lineDataDetails: {
    'A2-L1': {
      hourlyPassRates: [...],
      defectData: [...]
    }
  }
}
```

## 自定義配置

### 修改風險等級閾值
```javascript
const getRiskLevel = (score) => {
  if (score >= 60) return { class: 'risk-high', label: '高風險' }
  if (score >= 40) return { class: 'risk-medium', label: '中風險' }
  if (score >= 15) return { class: 'risk-low', label: '低風險' }
  return { class: 'risk-normal', label: '正常' }
}
```

### 調整智慧提示輪播時間
```javascript
const SMART_HINT_DURATION = 4000 // 4秒
```

## 性能優化

- 使用 `computed` 屬性緩存計算結果
- 組件懶加載
- 圖表按需渲染
- 響應式數據最小化

## 瀏覽器支持

- Chrome 88+
- Firefox 87+
- Safari 14+
- Edge 88+

## 開發建議

1. **數據接口**: 將 Mock 數據替換為真實 API 調用
2. **圖表庫**: 可考慮集成 ECharts 或 Chart.js 實現更豐富的圖表
3. **實時更新**: 使用 WebSocket 實現數據實時更新
4. **權限控制**: 根據用戶角色顯示不同內容
5. **國際化**: 使用 Vue I18n 支持多語言

## 維護說明

- 定期更新 Element Plus 版本
- 監控組件性能
- 優化響應式設計
- 更新瀏覽器兼容性
